"""
Comprehensive Import Validation Test
Tests all new components and their integration with the main system
"""

import sys
import os
import asyncio
import logging
import traceback
from typing import Dict, List, Any, Optional

# Add project root to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ImportValidationTest:
    """Comprehensive import validation for all new components"""
    
    def __init__(self):
        self.import_results = {}
        self.integration_results = {}
        self.failed_imports = []
        self.successful_imports = []
        
    def test_all_imports(self) -> Dict[str, Any]:
        """Test all imports and return comprehensive results"""
        logger.info("🧪 [IMPORT-TEST] Starting comprehensive import validation...")
        
        # Test core neural components
        self._test_neural_imports()
        
        # Test trading components
        self._test_trading_imports()
        
        # Test strategy components
        self._test_strategy_imports()
        
        # Test analysis components
        self._test_analysis_imports()
        
        # Test integration components
        self._test_integration_imports()
        
        # Generate summary
        summary = self._generate_test_summary()
        
        logger.info(f"🧪 [IMPORT-TEST] Validation complete: {summary['success_rate']:.1%} success rate")
        
        return summary

    def _test_neural_imports(self):
        """Test neural component imports"""
        logger.info("🧠 [NEURAL-TEST] Testing neural component imports...")
        
        neural_components = [
            # Enhanced neural components
            ('src.neural.enhanced_profit_predictor', 'EnhancedProfitPredictor'),
            ('src.neural.enhanced_risk_predictor', 'EnhancedRiskPredictor'),
            ('src.neural.neural_architecture_search', 'GeneticAlgorithmNAS'),
            ('src.neural.neural_architecture_search', 'ArchitectureBuilder'),
            ('src.neural.performance_optimizer', 'InferenceOptimizer'),
            ('src.neural.variational_autoencoder', 'MarketVAE'),
            ('src.neural.graph_neural_network', 'MarketGraphBuilder'),
            
            # Temporal intelligence
            ('src.neural.temporal_intelligence', 'AdvancedTemporalIntelligence'),
            ('src.neural.temporal_intelligence', 'TemporalIntelligenceSystem'),
            ('src.neural.multi_scale_time_analyzer', 'MultiScaleTimeAnalyzer'),
            
            # Persistent learning
            ('src.neural.persistent_learning_system', 'PersistentLearningSystem'),
            
            # Web research integration
            ('src.neural.web_research_integration', 'WebResearchIntegration'),
        ]
        
        for module_path, class_name in neural_components:
            self._test_single_import(module_path, class_name, 'neural')

    def _test_trading_imports(self):
        """Test trading component imports"""
        logger.info("💹 [TRADING-TEST] Testing trading component imports...")
        
        trading_components = [
            # Enhanced trading engines
            ('src.trading.enhanced_time_weighted_engine', 'EnhancedTimeWeightedEngine'),
            ('src.trading.advanced_twap_vwap_engine', 'AdvancedTWAPVWAPEngine'),
            ('src.trading.yield_optimization_engine', 'AdvancedYieldOptimizer'),
            ('src.trading.enhanced_cross_currency_arbitrage', 'EnhancedCrossCurrencyArbitrage'),
            
            # Existing trading components
            ('src.trading.multi_currency_trading_engine', 'MultiCurrencyTradingEngine'),
            ('src.trading.cross_currency_arbitrage_engine', 'CrossCurrencyArbitrageEngine'),
            ('src.trading.dynamic_portfolio_rebalancer', 'DynamicPortfolioRebalancer'),
            ('src.trading.advanced_order_types', 'AdvancedOrderExecutor'),
            ('src.trading.intelligent_liquidity_manager', 'IntelligentLiquidityManager'),
            ('src.trading.balance_aware_order_manager', 'BalanceAwareOrderManager'),
            ('src.trading.intelligent_currency_switcher', 'IntelligentCurrencySwitcher'),
            ('src.trading.cross_exchange_capital_manager', 'CrossExchangeCapitalManager'),
            ('src.trading.robust_error_recovery', 'RobustErrorRecovery'),
        ]
        
        for module_path, class_name in trading_components:
            self._test_single_import(module_path, class_name, 'trading')

    def _test_strategy_imports(self):
        """Test strategy component imports"""
        logger.info("📈 [STRATEGY-TEST] Testing strategy component imports...")
        
        strategy_components = [
            # New strategy components
            ('src.strategies.liquidity_aware_trading', 'LiquidityAwareTradingStrategy'),
            ('src.strategies.time_of_day_strategies', 'TimeOfDayStrategies'),
            
            # Existing strategy components
            ('src.strategies.high_frequency_trading', 'HighFrequencyTradingStrategy'),
            ('src.strategies.triangular_arbitrage', 'TriangularArbitrageStrategy'),
            ('src.strategies.statistical_arbitrage', 'StatisticalArbitrageStrategy'),
        ]
        
        for module_path, class_name in strategy_components:
            self._test_single_import(module_path, class_name, 'strategy')

    def _test_analysis_imports(self):
        """Test analysis component imports"""
        logger.info("📊 [ANALYSIS-TEST] Testing analysis component imports...")
        
        analysis_components = [
            # New analysis components
            ('src.analysis.order_book_analyzer', 'AdvancedOrderBookAnalyzer'),
            
            # Existing analysis components
            ('src.data_analysis.advanced_data_utilization', 'AdvancedDataUtilization'),
            ('src.data_analysis.real_time_sentiment_analyzer', 'RealTimeSentimentAnalyzer'),
            ('src.data_analysis.market_regime_detector', 'MarketRegimeDetector'),
        ]
        
        for module_path, class_name in analysis_components:
            self._test_single_import(module_path, class_name, 'analysis')

    def _test_integration_imports(self):
        """Test integration and core component imports"""
        logger.info("🔧 [INTEGRATION-TEST] Testing integration component imports...")
        
        integration_components = [
            # Core engine components
            ('src.core.engine', 'MarketMicrostructureAnalyzer'),
            ('src.core.engine', 'TimeWeightedDecisionEngine'),
            ('src.core.engine', 'MultiVariableProfitOptimizer'),
            
            # Exchange management
            ('src.trading.enhanced_exchange_manager', 'EnhancedExchangeManager'),
            
            # Data feeds
            ('src.data_feeds.real_time_validator', 'RealTimeDataValidator'),
            ('src.data_feeds.live_data_fetcher', 'LiveDataFetcher'),
            
            # Monitoring
            ('src.monitoring.endless_loop_validator', 'EndlessLoopValidator'),
        ]
        
        for module_path, class_name in integration_components:
            self._test_single_import(module_path, class_name, 'integration')

    def _test_single_import(self, module_path: str, class_name: str, category: str):
        """Test a single import"""
        try:
            # Import the module
            module = __import__(module_path, fromlist=[class_name])
            
            # Get the class
            cls = getattr(module, class_name)
            
            # Basic validation
            if cls and callable(cls):
                self.successful_imports.append({
                    'module': module_path,
                    'class': class_name,
                    'category': category,
                    'status': 'success'
                })
                logger.debug(f"✅ [IMPORT] {module_path}.{class_name}")
            else:
                raise ImportError(f"Class {class_name} not found or not callable")
                
        except Exception as e:
            self.failed_imports.append({
                'module': module_path,
                'class': class_name,
                'category': category,
                'error': str(e),
                'traceback': traceback.format_exc()
            })
            logger.warning(f"❌ [IMPORT] {module_path}.{class_name}: {e}")

    def _generate_test_summary(self) -> Dict[str, Any]:
        """Generate comprehensive test summary"""
        total_tests = len(self.successful_imports) + len(self.failed_imports)
        success_rate = len(self.successful_imports) / total_tests if total_tests > 0 else 0
        
        # Group by category
        category_stats = {}
        for import_result in self.successful_imports + self.failed_imports:
            category = import_result['category']
            if category not in category_stats:
                category_stats[category] = {'success': 0, 'failed': 0}
            
            if import_result in self.successful_imports:
                category_stats[category]['success'] += 1
            else:
                category_stats[category]['failed'] += 1
        
        return {
            'total_tests': total_tests,
            'successful_imports': len(self.successful_imports),
            'failed_imports': len(self.failed_imports),
            'success_rate': success_rate,
            'category_stats': category_stats,
            'failed_details': self.failed_imports,
            'successful_details': self.successful_imports
        }

    async def test_component_initialization(self) -> Dict[str, Any]:
        """Test component initialization with mock parameters"""
        logger.info("🔧 [INIT-TEST] Testing component initialization...")
        
        initialization_results = {}
        
        # Test neural component initialization
        await self._test_neural_initialization(initialization_results)
        
        # Test trading component initialization
        await self._test_trading_initialization(initialization_results)
        
        # Test strategy component initialization
        await self._test_strategy_initialization(initialization_results)
        
        return initialization_results

    async def _test_neural_initialization(self, results: Dict[str, Any]):
        """Test neural component initialization"""
        try:
            # Test PersistentLearningSystem
            from src.neural.persistent_learning_system import PersistentLearningSystem
            persistent_learning = PersistentLearningSystem(data_dir="test_data")
            results['persistent_learning'] = {'status': 'success', 'instance': type(persistent_learning).__name__}
            
            # Test AdvancedYieldOptimizer
            from src.trading.yield_optimization_engine import AdvancedYieldOptimizer
            yield_optimizer = AdvancedYieldOptimizer(exchange_clients={}, config={})
            results['yield_optimizer'] = {'status': 'success', 'instance': type(yield_optimizer).__name__}
            
            logger.info("✅ [INIT-TEST] Neural components initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [INIT-TEST] Neural initialization failed: {e}")
            results['neural_init_error'] = str(e)

    async def _test_trading_initialization(self, results: Dict[str, Any]):
        """Test trading component initialization"""
        try:
            # Test EnhancedTimeWeightedEngine
            from src.trading.enhanced_time_weighted_engine import EnhancedTimeWeightedEngine
            time_engine = EnhancedTimeWeightedEngine(config={})
            results['time_engine'] = {'status': 'success', 'instance': type(time_engine).__name__}
            
            # Test AdvancedTWAPVWAPEngine
            from src.trading.advanced_twap_vwap_engine import AdvancedTWAPVWAPEngine
            twap_engine = AdvancedTWAPVWAPEngine(exchange_clients={}, config={})
            results['twap_engine'] = {'status': 'success', 'instance': type(twap_engine).__name__}
            
            logger.info("✅ [INIT-TEST] Trading components initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [INIT-TEST] Trading initialization failed: {e}")
            results['trading_init_error'] = str(e)

    async def _test_strategy_initialization(self, results: Dict[str, Any]):
        """Test strategy component initialization"""
        try:
            # Test LiquidityAwareTradingStrategy
            from src.strategies.liquidity_aware_trading import LiquidityAwareTradingStrategy
            liquidity_strategy = LiquidityAwareTradingStrategy(config={})
            results['liquidity_strategy'] = {'status': 'success', 'instance': type(liquidity_strategy).__name__}
            
            # Test TimeOfDayStrategies
            from src.strategies.time_of_day_strategies import TimeOfDayStrategies
            time_strategies = TimeOfDayStrategies(config={})
            results['time_strategies'] = {'status': 'success', 'instance': type(time_strategies).__name__}
            
            logger.info("✅ [INIT-TEST] Strategy components initialized successfully")
            
        except Exception as e:
            logger.error(f"❌ [INIT-TEST] Strategy initialization failed: {e}")
            results['strategy_init_error'] = str(e)

def run_comprehensive_tests():
    """Run all comprehensive tests"""
    logger.info("🚀 [TEST-RUNNER] Starting comprehensive validation tests...")
    
    # Create test instance
    validator = ImportValidationTest()
    
    # Run import tests
    import_results = validator.test_all_imports()
    
    # Run initialization tests
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        init_results = loop.run_until_complete(validator.test_component_initialization())
    finally:
        loop.close()
    
    # Print comprehensive results
    print("\n" + "="*80)
    print("🧪 COMPREHENSIVE IMPORT VALIDATION RESULTS")
    print("="*80)
    
    print(f"\n📊 IMPORT STATISTICS:")
    print(f"   Total Tests: {import_results['total_tests']}")
    print(f"   Successful: {import_results['successful_imports']}")
    print(f"   Failed: {import_results['failed_imports']}")
    print(f"   Success Rate: {import_results['success_rate']:.1%}")
    
    print(f"\n📈 BY CATEGORY:")
    for category, stats in import_results['category_stats'].items():
        total = stats['success'] + stats['failed']
        rate = stats['success'] / total if total > 0 else 0
        print(f"   {category.upper()}: {stats['success']}/{total} ({rate:.1%})")
    
    if import_results['failed_imports']:
        print(f"\n❌ FAILED IMPORTS:")
        for failed in import_results['failed_imports']:
            print(f"   {failed['module']}.{failed['class']}: {failed['error']}")
    
    print(f"\n🔧 INITIALIZATION RESULTS:")
    for component, result in init_results.items():
        if isinstance(result, dict) and result.get('status') == 'success':
            print(f"   ✅ {component}: {result['instance']}")
        else:
            print(f"   ❌ {component}: {result}")
    
    print("\n" + "="*80)
    
    # Return overall success
    overall_success = (import_results['success_rate'] >= 0.8 and 
                      len([r for r in init_results.values() 
                          if isinstance(r, dict) and r.get('status') == 'success']) >= 3)
    
    if overall_success:
        print("🎉 OVERALL RESULT: VALIDATION SUCCESSFUL")
    else:
        print("⚠️  OVERALL RESULT: VALIDATION NEEDS ATTENTION")
    
    return overall_success

if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
