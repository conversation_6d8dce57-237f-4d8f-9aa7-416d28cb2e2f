"""
Liquidity-Aware Trading Strategies
Advanced trading strategies that adapt to market microstructure and liquidity conditions
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from decimal import Decimal
from dataclasses import dataclass
from enum import Enum
import numpy as np

logger = logging.getLogger(__name__)

class LiquidityRegime(Enum):
    """Market liquidity regimes"""
    VERY_HIGH = "very_high"
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"
    VERY_LOW = "very_low"
    STRESSED = "stressed"

class ExecutionStrategy(Enum):
    """Order execution strategies"""
    AGGRESSIVE = "aggressive"  # Market orders, immediate execution
    PASSIVE = "passive"       # Limit orders, provide liquidity
    ADAPTIVE = "adaptive"     # Mix based on conditions
    ICEBERG = "iceberg"       # Large order slicing
    TWAP = "twap"            # Time-weighted average price
    VWAP = "vwap"            # Volume-weighted average price
    POV = "pov"              # Participation of volume

@dataclass
class LiquidityCondition:
    """Current liquidity conditions"""
    regime: LiquidityRegime
    spread_bps: float
    depth_ratio: float
    impact_cost: float
    volatility: float
    flow_toxicity: float
    timestamp: float

@dataclass
class ExecutionPlan:
    """Execution plan for an order"""
    strategy: ExecutionStrategy
    total_quantity: Decimal
    slice_size: Decimal
    num_slices: int
    time_horizon_minutes: int
    max_participation_rate: float
    urgency_score: float
    expected_cost_bps: float
    confidence: float

class LiquidityAwareTradingStrategy:
    """Advanced liquidity-aware trading strategy"""
    
    def __init__(self, config: Dict = None):
        self.config = config or {}
        
        # Strategy parameters
        self.max_market_impact = self.config.get('max_market_impact', 0.01)  # 1%
        self.max_participation_rate = self.config.get('max_participation_rate', 0.2)  # 20%
        self.urgency_threshold = self.config.get('urgency_threshold', 0.7)
        
        # Liquidity thresholds
        self.liquidity_thresholds = {
            'very_high': {'spread_bps': 5, 'depth_ratio': 10.0},
            'high': {'spread_bps': 10, 'depth_ratio': 5.0},
            'normal': {'spread_bps': 20, 'depth_ratio': 2.0},
            'low': {'spread_bps': 50, 'depth_ratio': 1.0},
            'very_low': {'spread_bps': 100, 'depth_ratio': 0.5}
        }
        
        # Performance tracking
        self.execution_history = []
        self.strategy_performance = {}
        
        logger.info("🌊 [LIQUIDITY] Liquidity-aware trading strategy initialized")

    async def analyze_liquidity_conditions(self, symbol: str, market_data: Dict, 
                                         order_size: Decimal) -> LiquidityCondition:
        """Analyze current liquidity conditions"""
        try:
            order_book = market_data.get('order_book', {})
            trades = market_data.get('recent_trades', [])
            
            # Calculate spread
            spread_bps = self._calculate_spread_bps(order_book)
            
            # Calculate depth ratio
            depth_ratio = self._calculate_depth_ratio(order_book, order_size)
            
            # Estimate impact cost
            impact_cost = self._estimate_impact_cost(order_book, order_size, trades)
            
            # Calculate volatility
            volatility = self._calculate_short_term_volatility(trades)
            
            # Calculate flow toxicity
            flow_toxicity = self._calculate_flow_toxicity(trades)
            
            # Determine liquidity regime
            regime = self._classify_liquidity_regime(spread_bps, depth_ratio, impact_cost)
            
            return LiquidityCondition(
                regime=regime,
                spread_bps=spread_bps,
                depth_ratio=depth_ratio,
                impact_cost=impact_cost,
                volatility=volatility,
                flow_toxicity=flow_toxicity,
                timestamp=time.time()
            )
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error analyzing liquidity conditions: {e}")
            return LiquidityCondition(
                regime=LiquidityRegime.NORMAL,
                spread_bps=20.0,
                depth_ratio=2.0,
                impact_cost=0.005,
                volatility=0.02,
                flow_toxicity=0.5,
                timestamp=time.time()
            )

    async def create_execution_plan(self, symbol: str, side: str, quantity: Decimal,
                                  liquidity_condition: LiquidityCondition,
                                  urgency: float = 0.5) -> ExecutionPlan:
        """Create optimal execution plan based on liquidity conditions"""
        try:
            # Determine execution strategy
            strategy = self._select_execution_strategy(liquidity_condition, urgency)
            
            # Calculate optimal slicing
            slice_size, num_slices = self._calculate_optimal_slicing(
                quantity, liquidity_condition, strategy
            )
            
            # Determine time horizon
            time_horizon = self._calculate_time_horizon(
                liquidity_condition, strategy, urgency
            )
            
            # Calculate participation rate
            participation_rate = self._calculate_participation_rate(
                liquidity_condition, strategy, urgency
            )
            
            # Estimate execution cost
            expected_cost = self._estimate_execution_cost(
                liquidity_condition, strategy, quantity
            )
            
            # Calculate confidence
            confidence = self._calculate_execution_confidence(
                liquidity_condition, strategy
            )
            
            return ExecutionPlan(
                strategy=strategy,
                total_quantity=quantity,
                slice_size=slice_size,
                num_slices=num_slices,
                time_horizon_minutes=time_horizon,
                max_participation_rate=participation_rate,
                urgency_score=urgency,
                expected_cost_bps=expected_cost,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error creating execution plan: {e}")
            return ExecutionPlan(
                strategy=ExecutionStrategy.ADAPTIVE,
                total_quantity=quantity,
                slice_size=quantity,
                num_slices=1,
                time_horizon_minutes=5,
                max_participation_rate=0.1,
                urgency_score=urgency,
                expected_cost_bps=20.0,
                confidence=0.5
            )

    def _calculate_spread_bps(self, order_book: Dict) -> float:
        """Calculate bid-ask spread in basis points"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return 100.0  # Wide spread for missing data
            
            best_bid = float(bids[0][0])
            best_ask = float(asks[0][0])
            
            if best_bid <= 0:
                return 100.0
            
            spread = best_ask - best_bid
            spread_bps = (spread / best_bid) * 10000
            
            return spread_bps
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating spread: {e}")
            return 50.0

    def _calculate_depth_ratio(self, order_book: Dict, order_size: Decimal) -> float:
        """Calculate depth ratio (available liquidity vs order size)"""
        try:
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            # Calculate total depth in top 10 levels
            bid_depth = sum(float(bid[1]) for bid in bids[:10])
            ask_depth = sum(float(ask[1]) for ask in asks[:10])
            
            total_depth = bid_depth + ask_depth
            
            if float(order_size) <= 0:
                return 10.0  # High ratio for zero order size
            
            depth_ratio = total_depth / float(order_size)
            return depth_ratio
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating depth ratio: {e}")
            return 2.0

    def _estimate_impact_cost(self, order_book: Dict, order_size: Decimal, 
                            trades: List[Dict]) -> float:
        """Estimate market impact cost"""
        try:
            # Simple impact model based on order book depth
            bids = order_book.get('bids', [])
            asks = order_book.get('asks', [])
            
            if not bids or not asks:
                return 0.01  # 1% default impact
            
            # Calculate cumulative depth needed
            remaining_size = float(order_size)
            total_cost = 0.0
            best_price = float(asks[0][0])  # Assume buy order
            
            for price, size in asks[:20]:  # Check top 20 levels
                level_price = float(price)
                level_size = float(size)
                
                if remaining_size <= 0:
                    break
                
                consumed = min(remaining_size, level_size)
                cost = (level_price - best_price) / best_price
                total_cost += cost * (consumed / float(order_size))
                remaining_size -= consumed
            
            # Add additional impact for remaining size
            if remaining_size > 0:
                total_cost += 0.005 * (remaining_size / float(order_size))
            
            return total_cost
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error estimating impact cost: {e}")
            return 0.005

    def _calculate_short_term_volatility(self, trades: List[Dict]) -> float:
        """Calculate short-term volatility from recent trades"""
        try:
            if len(trades) < 5:
                return 0.02  # Default 2% volatility
            
            prices = [float(trade.get('price', 0)) for trade in trades[-20:]]
            prices = [p for p in prices if p > 0]
            
            if len(prices) < 2:
                return 0.02
            
            # Calculate returns
            returns = []
            for i in range(1, len(prices)):
                ret = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(ret)
            
            # Calculate volatility (standard deviation of returns)
            if returns:
                volatility = np.std(returns)
                return min(0.1, volatility)  # Cap at 10%
            
            return 0.02
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating volatility: {e}")
            return 0.02

    def _calculate_flow_toxicity(self, trades: List[Dict]) -> float:
        """Calculate order flow toxicity"""
        try:
            if len(trades) < 3:
                return 0.5
            
            # Look for patterns indicating toxic flow
            toxicity_signals = 0
            total_signals = 0
            
            # Check for large trades
            trade_sizes = [float(trade.get('quantity', 0)) for trade in trades[-10:]]
            avg_size = sum(trade_sizes) / len(trade_sizes) if trade_sizes else 0
            
            for size in trade_sizes[-5:]:
                total_signals += 1
                if size > avg_size * 3:  # Very large trade
                    toxicity_signals += 1
            
            # Check for directional flow
            recent_sides = [trade.get('side') for trade in trades[-5:]]
            if len(set(recent_sides)) == 1:  # All same direction
                toxicity_signals += 1
            total_signals += 1
            
            toxicity = toxicity_signals / total_signals if total_signals > 0 else 0.5
            return toxicity
            
        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating flow toxicity: {e}")
            return 0.5

    def _classify_liquidity_regime(self, spread_bps: float, depth_ratio: float,
                                 impact_cost: float) -> LiquidityRegime:
        """Classify current liquidity regime"""
        try:
            # Score based on multiple factors
            if spread_bps <= 5 and depth_ratio >= 10 and impact_cost <= 0.001:
                return LiquidityRegime.VERY_HIGH
            elif spread_bps <= 10 and depth_ratio >= 5 and impact_cost <= 0.002:
                return LiquidityRegime.HIGH
            elif spread_bps <= 20 and depth_ratio >= 2 and impact_cost <= 0.005:
                return LiquidityRegime.NORMAL
            elif spread_bps <= 50 and depth_ratio >= 1 and impact_cost <= 0.01:
                return LiquidityRegime.LOW
            elif spread_bps <= 100 and depth_ratio >= 0.5:
                return LiquidityRegime.VERY_LOW
            else:
                return LiquidityRegime.STRESSED

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error classifying regime: {e}")
            return LiquidityRegime.NORMAL

    def _select_execution_strategy(self, liquidity_condition: LiquidityCondition,
                                 urgency: float) -> ExecutionStrategy:
        """Select optimal execution strategy"""
        try:
            regime = liquidity_condition.regime

            # High urgency favors aggressive strategies
            if urgency >= 0.8:
                if regime in [LiquidityRegime.VERY_HIGH, LiquidityRegime.HIGH]:
                    return ExecutionStrategy.AGGRESSIVE
                else:
                    return ExecutionStrategy.ADAPTIVE

            # Normal urgency adapts to liquidity
            elif urgency >= 0.4:
                if regime == LiquidityRegime.VERY_HIGH:
                    return ExecutionStrategy.PASSIVE
                elif regime in [LiquidityRegime.HIGH, LiquidityRegime.NORMAL]:
                    return ExecutionStrategy.ADAPTIVE
                else:
                    return ExecutionStrategy.TWAP

            # Low urgency favors patient strategies
            else:
                if regime in [LiquidityRegime.VERY_LOW, LiquidityRegime.STRESSED]:
                    return ExecutionStrategy.ICEBERG
                else:
                    return ExecutionStrategy.VWAP

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error selecting strategy: {e}")
            return ExecutionStrategy.ADAPTIVE

    def _calculate_optimal_slicing(self, quantity: Decimal,
                                 liquidity_condition: LiquidityCondition,
                                 strategy: ExecutionStrategy) -> Tuple[Decimal, int]:
        """Calculate optimal order slicing"""
        try:
            depth_ratio = liquidity_condition.depth_ratio

            if strategy == ExecutionStrategy.AGGRESSIVE:
                # Minimal slicing for aggressive execution
                return quantity, 1

            elif strategy == ExecutionStrategy.PASSIVE:
                # Small slices for passive execution
                slice_size = quantity / max(5, min(20, int(depth_ratio)))
                num_slices = int(quantity / slice_size)
                return slice_size, max(1, num_slices)

            elif strategy == ExecutionStrategy.ICEBERG:
                # Many small slices for large orders
                slice_size = quantity / max(10, min(50, int(float(quantity) / 100)))
                num_slices = int(quantity / slice_size)
                return slice_size, max(1, num_slices)

            else:  # ADAPTIVE, TWAP, VWAP, POV
                # Moderate slicing based on liquidity
                if depth_ratio >= 5:
                    slice_size = quantity / 3
                    num_slices = 3
                elif depth_ratio >= 2:
                    slice_size = quantity / 5
                    num_slices = 5
                else:
                    slice_size = quantity / 10
                    num_slices = 10

                return slice_size, num_slices

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating slicing: {e}")
            return quantity, 1

    def _calculate_time_horizon(self, liquidity_condition: LiquidityCondition,
                              strategy: ExecutionStrategy, urgency: float) -> int:
        """Calculate execution time horizon in minutes"""
        try:
            base_time = 5  # 5 minutes base

            # Adjust for strategy
            if strategy == ExecutionStrategy.AGGRESSIVE:
                time_horizon = 1  # Immediate execution
            elif strategy == ExecutionStrategy.PASSIVE:
                time_horizon = base_time * 3  # 15 minutes
            elif strategy == ExecutionStrategy.ICEBERG:
                time_horizon = base_time * 6  # 30 minutes
            else:
                time_horizon = base_time  # 5 minutes

            # Adjust for urgency
            urgency_factor = 2.0 - urgency  # Higher urgency = shorter time
            time_horizon = int(time_horizon * urgency_factor)

            # Adjust for liquidity regime
            if liquidity_condition.regime in [LiquidityRegime.VERY_LOW, LiquidityRegime.STRESSED]:
                time_horizon *= 2  # Take more time in poor liquidity

            return max(1, min(60, time_horizon))  # 1-60 minutes

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating time horizon: {e}")
            return 5

    def _calculate_participation_rate(self, liquidity_condition: LiquidityCondition,
                                    strategy: ExecutionStrategy, urgency: float) -> float:
        """Calculate maximum participation rate"""
        try:
            base_rate = 0.1  # 10% base participation

            # Adjust for strategy
            if strategy == ExecutionStrategy.AGGRESSIVE:
                participation_rate = 0.5  # Up to 50% for aggressive
            elif strategy == ExecutionStrategy.PASSIVE:
                participation_rate = 0.05  # Low participation for passive
            elif strategy == ExecutionStrategy.POV:
                participation_rate = 0.2  # 20% for POV
            else:
                participation_rate = base_rate

            # Adjust for liquidity regime
            if liquidity_condition.regime == LiquidityRegime.VERY_HIGH:
                participation_rate *= 2.0
            elif liquidity_condition.regime in [LiquidityRegime.VERY_LOW, LiquidityRegime.STRESSED]:
                participation_rate *= 0.5

            # Adjust for urgency
            participation_rate *= (0.5 + urgency)

            return min(0.5, max(0.01, participation_rate))

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating participation rate: {e}")
            return 0.1

    def _estimate_execution_cost(self, liquidity_condition: LiquidityCondition,
                               strategy: ExecutionStrategy, quantity: Decimal) -> float:
        """Estimate execution cost in basis points"""
        try:
            base_cost = liquidity_condition.spread_bps / 2  # Half spread as base

            # Add impact cost
            impact_cost_bps = liquidity_condition.impact_cost * 10000

            # Strategy adjustment
            if strategy == ExecutionStrategy.AGGRESSIVE:
                strategy_multiplier = 1.5  # Higher cost for aggressive
            elif strategy == ExecutionStrategy.PASSIVE:
                strategy_multiplier = 0.5  # Lower cost for passive
            else:
                strategy_multiplier = 1.0

            total_cost = (base_cost + impact_cost_bps) * strategy_multiplier

            return min(100.0, max(1.0, total_cost))  # 1-100 bps range

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error estimating execution cost: {e}")
            return 20.0

    def _calculate_execution_confidence(self, liquidity_condition: LiquidityCondition,
                                      strategy: ExecutionStrategy) -> float:
        """Calculate confidence in execution plan"""
        try:
            confidence = 0.5  # Base confidence

            # Adjust for liquidity regime
            regime_confidence = {
                LiquidityRegime.VERY_HIGH: 0.95,
                LiquidityRegime.HIGH: 0.85,
                LiquidityRegime.NORMAL: 0.75,
                LiquidityRegime.LOW: 0.6,
                LiquidityRegime.VERY_LOW: 0.4,
                LiquidityRegime.STRESSED: 0.2
            }

            confidence = regime_confidence.get(liquidity_condition.regime, 0.5)

            # Adjust for flow toxicity
            confidence *= (1.0 - liquidity_condition.flow_toxicity * 0.3)

            # Adjust for volatility
            confidence *= (1.0 - min(0.5, liquidity_condition.volatility * 10))

            return max(0.1, min(0.95, confidence))

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error calculating confidence: {e}")
            return 0.5

    async def execute_liquidity_aware_trade(self, symbol: str, side: str,
                                          quantity: Decimal, market_data: Dict,
                                          urgency: float = 0.5) -> Dict:
        """Execute a trade using liquidity-aware strategy"""
        try:
            start_time = time.time()

            # Analyze liquidity conditions
            liquidity_condition = await self.analyze_liquidity_conditions(
                symbol, market_data, quantity
            )

            # Create execution plan
            execution_plan = await self.create_execution_plan(
                symbol, side, quantity, liquidity_condition, urgency
            )

            # Log execution plan
            logger.info(f"🌊 [LIQUIDITY] Execution plan for {symbol}: "
                       f"Strategy={execution_plan.strategy.value}, "
                       f"Slices={execution_plan.num_slices}, "
                       f"Cost={execution_plan.expected_cost_bps:.1f}bps")

            execution_time = (time.time() - start_time) * 1000

            return {
                'success': True,
                'liquidity_condition': liquidity_condition,
                'execution_plan': execution_plan,
                'execution_time_ms': execution_time,
                'strategy_type': 'liquidity_aware'
            }

        except Exception as e:
            logger.error(f"❌ [LIQUIDITY] Error in liquidity-aware execution: {e}")
            return {
                'success': False,
                'error': str(e),
                'strategy_type': 'liquidity_aware'
            }
